import { useEffect, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';

/**
 * Custom hook for auto-staggering children animations
 *
 * This hook automatically applies staggered animations to all direct children
 * of a container element when it comes into view. Each child will animate
 * with a progressive delay, creating a smooth staggered effect.
 *
 * @param baseDelay - Initial delay before the first animation starts (in ms)
 * @param staggerDelay - Delay between each child animation (in ms)
 * @param threshold - Intersection observer threshold (0-1)
 * @param duration - Animation duration (in ms)
 * @param direction - Animation direction: 'x' for horizontal, 'y' for vertical
 * @returns Object with ref, isVisible state, and containerRef
 *
 * @example
 * ```tsx
 * // Horizontal animation (like AboutSection)
 * const stagger = useStaggeredAnimation(500, 200, 0.3, 1000, 'x');
 *
 * return (
 *   <div ref={stagger.ref} className="container">
 *     <div className="opacity-0 translate-x-8">Item 1</div>
 *     <div className="opacity-0 translate-x-8">Item 2</div>
 *   </div>
 * );
 * ```
 */
export const useStaggeredAnimation = (
  baseDelay = 0,
  staggerDelay = 200,
  threshold = 0.3,
  duration = 200,
  direction: 'x' | 'y' = 'y'
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  
  const { ref: inViewRef, inView } = useInView({
    threshold,
    triggerOnce: true,
  });
  
  useEffect(() => {
    if (inView) {
      setIsVisible(true);

      // Apply staggered animations to children by manipulating classes
      if (containerRef.current) {
        const children = Array.from(containerRef.current.children) as HTMLElement[];
        children.forEach((child, index) => {
          const delay = baseDelay + (index * staggerDelay);

          // Set only the transition delay since duration/easing are in Tailwind classes
          child.style.transitionDelay = `${delay}ms`;

          // Remove initial hidden state classes and add visible state
          setTimeout(() => {
            child.classList.remove('opacity-0');
            child.classList.add('opacity-100');

            if (direction === 'x') {
              child.classList.remove('translate-x-8');
              child.classList.add('translate-x-0');
            } else {
              child.classList.remove('translate-y-8');
              child.classList.add('translate-y-0');
            }
          }, delay);
        });
      }
    }
  }, [inView, isVisible, baseDelay, staggerDelay, duration, direction]);
  
  // Combine refs
  const setRefs = (element: HTMLDivElement | null) => {
    containerRef.current = element;
    inViewRef(element);
  };
  
  return {
    ref: setRefs,
    isVisible,
    containerRef
  };
};

export default useStaggeredAnimation;
