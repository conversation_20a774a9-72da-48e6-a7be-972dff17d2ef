/* CTA Section - Light Gradient Animation */

@keyframes cta-gradient-animation {
    0% {
        --c-0: hsla(216, 27%, 76%, 1);
        --x-0: 27%;
        --y-0: 28%;
        --s-start-0: 10%;
        --s-end-0: 78%;
        --c-1: hsla(224, 15%, 84%, 1);
        --x-1: 49%;
        --y-1: 95%;
        --s-start-1: 10%;
        --s-end-1: 78%;
        --c-2: hsla(263, 32%, 90%, 1);
        --x-2: 31%;
        --y-2: 60%;
        --s-start-2: 10%;
        --s-end-2: 78%;
        --c-3: hsla(205, 14%, 100%, 1);
        --x-3: 3%;
        --y-3: 77%;
        --s-start-3: 10%;
        --s-end-3: 78%;
        --c-4: hsla(237, 19%, 98%, 1);
        --x-4: 51%;
        --y-4: 55%;
        --s-start-4: 10%;
        --s-end-4: 78%;
    }

    50% {
        --c-0: hsla(209.38232870662932, 100%, 87%, 1);
        --x-0: 9%;
        --y-0: 73%;
        --s-start-0: 10%;
        --s-end-0: 78%;
        --c-1: hsla(308.6470345889821, 100%, 91%, 1);
        --x-1: 30%;
        --y-1: 41%;
        --s-start-1: 10%;
        --s-end-1: 78%;
        --c-2: hsla(341, 15%, 82%, 1);
        --x-2: 77%;
        --y-2: 73%;
        --s-start-2: 10%;
        --s-end-2: 78%;
        --c-3: hsla(212, 1%, 99%, 1);
        --x-3: 34%;
        --y-3: 74%;
        --s-start-3: 10%;
        --s-end-3: 78%;
        --c-4: hsla(242, 16%, 97%, 1);
        --x-4: 21%;
        --y-4: 55%;
        --s-start-4: 10%;
        --s-end-4: 78%;
    }

    100% {
        --c-0: hsla(183, 19%, 93%, 1);
        --x-0: 2%;
        --y-0: 54%;
        --s-start-0: 10%;
        --s-end-0: 78%;
        --c-1: hsla(202, 31%, 96%, 1);
        --x-1: 43%;
        --y-1: 10%;
        --s-start-1: 10%;
        --s-end-1: 78%;
        --c-2: hsla(245, 21%, 97%, 1);
        --x-2: 35%;
        --y-2: 12%;
        --s-start-2: 10%;
        --s-end-2: 78%;
        --c-3: hsla(231, 19%, 84%, 1);
        --x-3: 3%;
        --y-3: 56%;
        --s-start-3: 10%;
        --s-end-3: 78%;
        --c-4: hsla(227, 1%, 100%, 1);
        --x-4: 9%;
        --y-4: 2%;
        --s-start-4: 10%;
        --s-end-4: 78%;
    }
}

@property --c-0 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(216, 27%, 76%, 1)
}

@property --x-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 27%
}

@property --y-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 28%
}

@property --s-start-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 78%
}

@property --c-1 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(224, 15%, 84%, 1)
}

@property --x-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 49%
}

@property --y-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 95%
}

@property --s-start-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 78%
}

@property --c-2 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(263, 32%, 90%, 1)
}

@property --x-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 31%
}

@property --y-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 60%
}

@property --s-start-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 78%
}

@property --c-3 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(205, 14%, 100%, 1)
}

@property --x-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 3%
}

@property --y-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 77%
}

@property --s-start-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 78%
}

@property --c-4 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(237, 19%, 98%, 1)
}

@property --x-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 51%
}

@property --y-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 55%
}

@property --s-start-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 78%
}

.cta-gradient-bg {
    --c-0: hsla(216, 27%, 76%, 1);
    --x-0: 27%;
    --y-0: 28%;
    --c-1: hsla(224, 15%, 84%, 1);
    --x-1: 49%;
    --y-1: 95%;
    --c-2: hsla(263, 32%, 90%, 1);
    --x-2: 31%;
    --y-2: 60%;
    --c-3: hsla(205, 14%, 100%, 1);
    --x-3: 3%;
    --y-3: 77%;
    --c-4: hsla(237, 19%, 98%, 1);
    --x-4: 51%;
    --y-4: 55%;
    background-color: hsla(333, 19%, 89%, 1);
    background-image: radial-gradient(circle at var(--x-0) var(--y-0), var(--c-0) var(--s-start-0), transparent var(--s-end-0)), radial-gradient(circle at var(--x-1) var(--y-1), var(--c-1) var(--s-start-1), transparent var(--s-end-1)), radial-gradient(circle at var(--x-2) var(--y-2), var(--c-2) var(--s-start-2), transparent var(--s-end-2)), radial-gradient(circle at var(--x-3) var(--y-3), var(--c-3) var(--s-start-3), transparent var(--s-end-3)), radial-gradient(circle at var(--x-4) var(--y-4), var(--c-4) var(--s-start-4), transparent var(--s-end-4));
    animation: cta-gradient-animation 3s ease-in-out infinite alternate;
    background-blend-mode: normal, normal, normal, normal, normal;
}
