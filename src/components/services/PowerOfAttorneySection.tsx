'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';

const PowerOfAttorneySection = () => {
  const t = useTranslations('servicesPage.powerOfAttorney');

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: helpTextRef, inView: helpTextInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // Use staggered animation for requirements
  const requirementsStagger = useStaggeredAnimation(400, 200, 0.3, 1000, 'y');

  const requirements = [
    { title: t('requirements.0.title'), description: t('requirements.0.description') },
    { title: t('requirements.1.title'), description: t('requirements.1.description') },
    { title: t('requirements.2.title'), description: t('requirements.2.description') },
    { title: t('requirements.3.title'), description: t('requirements.3.description') },
  ];

  return (
    <section ref={backgroundRef} className="py-24 relative overflow-hidden">
      {/* CSS Gradient Background */}
      <div
        className={`features-gradient-bg absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>
      <div className="grain !opacity-25"></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        {/* Title Section */}
        <div className="flex flex-col md:flex-row gap-8">
          <div className="md:w-1/2">
            <p className="text-sm text-albatros-ivory font-light mb-8">
              <span className="font-bold">Albatros</span><br />
              {t('subtitle')}
            </p>
                        <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-6xl font-normal text-albatros-ivory leading-tight">
                {t('title')}
              </h2>
            </div>
          </div>
          <div className="md:w-1/2">
            <div
              ref={requirementsStagger.ref}
              className="mb-8 flex flex-col gap-8"
            >
              {requirements.map((requirement, index) => (
                <div
                  key={index}
                  className="opacity-0 translate-y-8 transition-all duration-1000 ease-out"
                >
                  <div className="bg-albatros-ivory/10 backdrop-blur-sm rounded-lg p-8 h-full border border-albatros-ivory/20">
                    <div className="text-albatros-ivory text-lg font-medium mb-2">
                      {String(index + 1).padStart(2, '0')}
                    </div>
                    <h3 className="text-xl font-bold text-albatros-ivory mb-2 leading-tight">
                      {requirement.title}
                    </h3>
                    <p className="text-albatros-ivory/90 leading-relaxed">
                      {requirement.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div
              ref={helpTextRef}
              className={`transition-all duration-1000 ease-out delay-700 ${
                helpTextInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
                <p className="text-albatros-ivory text-sm border-t border-albatros-ivory/20 pt-8 leading-tight font-semibold">
                  {t('helpText')}
                </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PowerOfAttorneySection;
