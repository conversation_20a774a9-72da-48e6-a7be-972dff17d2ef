import React from 'react';
import { cn } from '@/lib/utils';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'white' | 'accent' | 'ghost' | 'outline' | 'outlinewhite';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  isLoading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    variant = 'primary', 
    size = 'md', 
    children, 
    className, 
    isLoading = false,
    disabled,
    ...props 
  }, ref) => {
    const baseStyles = `
      inline-flex items-center justify-center font-medium
      transition-all duration-300 ease-in-out
      focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
      cursor-pointer rounded-full
      [background-size:100%_100%] text-xl
    `;

    const variants = {
      primary: `
        bg-gradient-to-r from-albatros-black to-albatros-indigo-dye
        hover:[background-size:140%_150%]
        text-albatros-ivory shadow-md
        hover:shadow-2xl
        focus-visible:ring-albatros-moonstone
      `,
      secondary: `
        bg-gradient-to-r from-albatros-moonstone to-albatros-ivory
        hover:[background-size:130%_120%]
        text-albatros-black shadow-md
        hover:shadow-xl
        focus-visible:ring-albatros-moonstone
      `,
      white: `
        bg-white
        border-2 border-white
        hover:bg-transparent
        text-black shadow-md
        hover:text-white
        hover:shadow-2xl
        focus-visible:ring-albatros-moonstone
      `,
      accent: `
        bg-gradient-to-r from-albatros-carmine to-albatros-carmine/60
        hover:[background-size:150%_130%]
        text-albatros-ivory shadow-md
        hover:shadow-xl
        focus-visible:ring-albatros-carmine
      `,
      ghost: `
        bg-gradient-to-r from-transparent to-albatros-indigo-dye/5
        hover:[background-size:120%_110%]
        text-albatros-indigo-dye
        hover:text-albatros-black hover:shadow-md
        focus-visible:ring-albatros-indigo-dye
      `,
      outline: `
        border-2 border-black text-black
        bg-transparent
        shadow-sm
        hover:border-albatros-ivory
        hover:text-albatros-ivory
        hover:bg-black/80
        hover:shadow-xl
        focus-visible:ring-albatros-indigo-dye
      `,
      outlinewhite: `
        border-2 border-albatros-ivory text-albatros-ivory
        bg-transparent
        shadow-sm
        hover:border-albatros-ivory
        hover:text-black
        hover:bg-albatros-ivory
        hover:shadow-xl
        focus-visible:ring-albatros-indigo-dye
      `
    };

    const sizes = {
      sm: 'px-6 py-2 text-sm',
      md: 'px-10 py-5 font-normal',
      lg: 'px-12 py-5 font-normal'
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading && (
          <svg
            className="animate-spin h-4 w-4 mr-2"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
