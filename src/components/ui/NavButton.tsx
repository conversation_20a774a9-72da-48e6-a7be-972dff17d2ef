import React from 'react';
import { cn } from '@/lib/utils';

export interface NavButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'language' | 'mobile' | 'dropdown-item';
  isScrolled?: boolean;
  isActive?: boolean;
  children: React.ReactNode;
  className?: string;
}

const NavButton = React.forwardRef<HTMLButtonElement, NavButtonProps>(
  ({
    variant = 'language',
    isScrolled = false,
    isActive = false,
    children,
    className,
    ...props
  }, ref) => {
    const baseStyles = `
      transition-all duration-150 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
      focus-visible:ring-albatros-moonstone cursor-pointer
    `;

    const variants = {
      language: `
        flex items-center space-x-3 px-5 py-3 text-sm font-medium rounded-full
        ${isScrolled
          ? `glass border border-white/20 text-albatros-ivory/90 hover:text-albatros-ivory
             hover:bg-white/10`
          : `glass border border-white/30 text-albatros-ivory/95 hover:text-albatros-ivory
             hover:bg-white/15 backdrop-blur-md drop-shadow-lg`
        }
      `,
      mobile: `
        p-3 rounded-full
        ${isScrolled
          ? `glass border border-white/20 text-albatros-ivory/90 hover:text-albatros-ivory
             hover:bg-white/10`
          : `glass border border-white/30 text-albatros-ivory/95 hover:text-albatros-ivory
             hover:bg-white/15 backdrop-blur-md drop-shadow-lg`
        }
      `,
      'dropdown-item': `
        flex items-center space-x-3 w-full pl-3 pr-10 py-2.5 text-sm rounded-xl
        transition-all duration-150
        ${isActive
          ? `bg-white/15 text-albatros-ivory`
          : `text-albatros-ivory/80 hover:text-albatros-ivory hover:bg-white/10`
        }
      `
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
);

NavButton.displayName = 'NavButton';

export default NavButton;
