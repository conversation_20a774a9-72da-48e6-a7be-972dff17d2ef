'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';

const AboutHeroSection = () => {
  const t = useTranslations('about.hero');

  // Intersection observers for staggered animations
  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: descriptionRef, inView: descriptionInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: borderRef, inView: borderInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section className="relative h-screen pb-20 pt-28 flex items-end justify-center overflow-hidden">
      {/* Hero Background Image */}
      <div className="absolute inset-0 z-0 animate-fade-in-hero-bg brightness-70">
        <Image
          src="/images/holding-doc.jpg"
          alt="AlbatrosDoc About Background"
          fill
          priority
          className="object-cover"
          sizes="100vw"
        />
      </div>
      <div className="grain !opacity-30"></div>

      <div className="relative h-full w-full mt-10 max-w-7xl mx-auto px-6 lg:px-8 z-20 animate-slide-in-hero">
        <div
          ref={borderRef}
          className="relative space-y-8 h-full mt-auto flex flex-col justify-end lg:max-w-[60%]"
        >
          {/* Animated border */}
          <div
            className={`absolute mb-0 bottom-0 right-0 w-px bg-albatros-ivory/20 transition-all duration-1000 ease-out delay-1600 origin-bottom ${
              borderInView
                ? 'h-full opacity-100 scale-y-100'
                : 'h-full opacity-0 scale-y-0'
            }`}
          ></div>
          
          {/* Small subtitle */}
          <div className="space-y-8 md:pr-8">
            <p className="text-sm text-albatros-ivory/80 font-light uppercase tracking-wider">
              {t('subtitle')}
            </p>
            
            <h1
              ref={titleRef}
              className={`text-4xl lg:text-5xl xl:text-6xl font-bold block text-albatros-ivory mb-4 transition-all duration-1000 ease-out delay-700 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              {t('title')}
            </h1>

            <div
              ref={descriptionRef}
              className={`transition-all duration-1000 ease-out delay-1000 ${
                descriptionInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <p className="text-xl lg:text-2xl text-albatros-ivory/90 font-light leading-relaxed">
                {t('description')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutHeroSection;
